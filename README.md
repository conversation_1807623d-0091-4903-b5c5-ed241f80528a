# Golang Todo App

A simple REST API for managing TODO tasks built with Go and Gorilla Mux.

## Features

- Create new TODO tasks
- Retrieve all tasks
- Retrieve a specific task by ID
- Update existing tasks
- Delete tasks
- JSON API responses

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/tasks` | Get all tasks |
| GET | `/gettask/{id}` | Get a specific task by ID |
| POST | `/task` | Create a new task |
| PUT | `/task/{id}` | Update an existing task |
| DELETE | `/task/{id}` | Delete a task |

## Project Structure

```
golang_todo/
├── src/
│   ├── main.go      # Main application file
│   ├── go.mod       # Go module file
│   └── go.sum       # Go dependencies
└── README.md
```

## Getting Started

### Prerequisites

- Go 1.19 or higher
- Git

### Installation

1. Clone the repository:
```bash
git clone https://github.com/vivekprasad99/Golang_Todo_App.git
cd Golang_Todo_App
```

2. Navigate to the src directory:
```bash
cd src
```

3. Install dependencies:
```bash
go mod tidy
```

4. Run the application:
```bash
go run main.go
```

The server will start on `http://localhost:8000`

## Usage Examples

### Create a new task
```bash
curl -X POST http://localhost:8000/task \
  -H "Content-Type: application/json" \
  -d '{"id":"1","title":"Learn Go","desc":"Study Go programming language"}'
```

### Get all tasks
```bash
curl http://localhost:8000/tasks
```

### Get a specific task
```bash
curl http://localhost:8000/gettask/1
```

### Update a task
```bash
curl -X PUT http://localhost:8000/task/1 \
  -H "Content-Type: application/json" \
  -d '{"id":"1","title":"Master Go","desc":"Become proficient in Go programming"}'
```

### Delete a task
```bash
curl -X DELETE http://localhost:8000/task/1
```

## Dependencies

- [Gorilla Mux](https://github.com/gorilla/mux) - HTTP router and URL matcher

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is open source and available under the [MIT License](LICENSE).
